import { type NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import type { Database } from "@reality-scripts/lib"

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const resolvedParams = await params
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get video by ID
    const { data: video, error } = await supabase
      .from("videos")
      .select("*")
      .eq("id", resolvedParams.id)
      .eq("user_id", session.user.id)
      .single()

    if (error) {
      if (error.code === "PGRST116") {
        return NextResponse.json({ error: "Video not found" }, { status: 404 })
      }
      console.error("Error fetching video:", error)
      return NextResponse.json({ error: "Failed to fetch video" }, { status: 500 })
    }

    // Check if video belongs to user
    if (video.user_id !== session.user.id) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json(video)
  } catch (error) {
    console.error("Video GET API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const resolvedParams = await params
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    // Validate allowed update fields
    const allowedFields = ["title", "description", "locale", "status"]
    const updateData: any = {}

    for (const [key, value] of Object.entries(body)) {
      if (allowedFields.includes(key)) {
        updateData[key] = value
      } else {
        return NextResponse.json({ error: `Invalid update field: ${key}` }, { status: 400 })
      }
    }

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({ error: "No valid fields to update" }, { status: 400 })
    }

    // Update video
    const { data: video, error } = await supabase
      .from("videos")
      .update(updateData)
      .eq("id", resolvedParams.id)
      .eq("user_id", session.user.id)
      .select()
      .single()

    if (error) {
      if (error.code === "PGRST116") {
        return NextResponse.json({ error: "Video not found" }, { status: 404 })
      }
      console.error("Error updating video:", error)
      return NextResponse.json({ error: "Failed to update video" }, { status: 500 })
    }

    return NextResponse.json(video)
  } catch (error) {
    console.error("Video PUT API error:", error)
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: "Invalid JSON" }, { status: 400 })
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies })

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get video to check ownership and get storage path
    const { data: video, error: fetchError } = await supabase
      .from("videos")
      .select("*")
      .eq("id", params.id)
      .eq("user_id", session.user.id)
      .single()

    if (fetchError) {
      if (fetchError.code === "PGRST116") {
        return NextResponse.json({ error: "Video not found" }, { status: 404 })
      }
      console.error("Error fetching video for deletion:", fetchError)
      return NextResponse.json({ error: "Failed to fetch video" }, { status: 500 })
    }

    // Delete from storage
    if (video.storage_path) {
      const { error: storageError } = await supabase.storage.from("raw").remove([video.storage_path])
      if (storageError) {
        console.error("Error deleting from storage:", storageError)
        // Continue with database deletion even if storage deletion fails
      }
    }

    // Delete video record
    const { error: deleteError } = await supabase
      .from("videos")
      .delete()
      .eq("id", params.id)
      .eq("user_id", session.user.id)

    if (deleteError) {
      console.error("Error deleting video:", deleteError)
      return NextResponse.json({ error: "Failed to delete video" }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Video DELETE API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
