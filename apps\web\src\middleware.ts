import { type NextRequest, NextResponse } from "next/server"
import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs"
import type { Database } from "@reality-scripts/lib"

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()

  try {
    const supabase = createMiddlewareClient<Database>({
      req: req as any, // Type assertion to fix Next.js version compatibility
      res: res as any
    })

    // Refresh session if expired
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) {
      console.error("Session error:", sessionError)
      // Clear potentially corrupted session
      const response = NextResponse.redirect(new URL("/login", req.url))
      response.cookies.delete("sb-auth-token")
      return response
    }

    // Check if the request is for a protected route
    const isProtectedRoute =
      req.nextUrl.pathname.startsWith("/dashboard") || req.nextUrl.pathname.startsWith("/api/videos")
    const isAuthRoute = req.nextUrl.pathname.startsWith("/login") || req.nextUrl.pathname.startsWith("/register")
    const isPublicApiRoute =
      req.nextUrl.pathname.startsWith("/api/health") || req.nextUrl.pathname.startsWith("/api/metrics")

    // Allow public API routes
    if (isPublicApiRoute) {
      return res
    }

    // If accessing a protected route without a session, redirect to login
    if (isProtectedRoute && !session) {
      const redirectUrl = new URL("/login", req.url)
      redirectUrl.searchParams.set("redirect", req.nextUrl.pathname)
      return NextResponse.redirect(redirectUrl)
    }

    // If accessing auth routes with a session, redirect to dashboard
    if (isAuthRoute && session) {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    // Add security headers
    const response = NextResponse.next()
    response.headers.set("X-Frame-Options", "DENY")
    response.headers.set("X-Content-Type-Options", "nosniff")
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin")
    response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()")

    if (req.nextUrl.protocol === "https:") {
      response.headers.set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
    }

    return response
  } catch (error) {
    console.error("Middleware error:", error)
    // On error, allow the request to proceed but log the issue
    return res
  }
}

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/login",
    "/register",
    "/api/videos/:path*",
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
}
