/**
 * Jest DOM Type Extensions
 * 
 * This file extends Jest matchers with @testing-library/jest-dom matchers
 * and custom test utilities for comprehensive testing support.
 */

import '@testing-library/jest-dom'

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R
      toHaveAttribute(attr: string, value?: string): R
      toHaveClass(className: string): R
      toHaveValue(value: string | number): R
      toBeEnabled(): R
      toBeDisabled(): R
      toBeVisible(): R
      toBeHidden(): R
      toHaveAccessibleName(name?: string): R
      toStartWith(prefix: string): R
      toBeValidVideo(): R
      toBeValidAudio(): R
    }
  }

  // Global test utilities type definitions
  namespace globalThis {
    var testUtils: {
      mockUserSession: {
        user: {
          id: string
          email: string
          created_at: string
        }
        access_token: string
        refresh_token: string
      }
      mockVideoData: {
        id: string
        user_id: string
        storage_path: string
        locale: string
        expires_at: string
        status: 'failed' | 'transcribed' | 'processing' | 'uploaded' | 'processed' | 'expired'
        created_at: string
      }
      mockTranscriptionData: {
        id: string
        video_id: string
        raw: { segments: any[] }
        text_clean: string
        words: Array<{
          text: string
          start: number
          end: number
        }>
        language: string
        created_at: string
      }
    }
  }
}

export {}
