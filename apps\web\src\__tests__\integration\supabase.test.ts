import { DatabaseService } from "@reality-scripts/lib"

// Mock Supabase client for testing
const mockSupabaseClient = {
  auth: {
    getSession: jest.fn(),
    getUser: jest.fn(),
    signInWithPassword: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn(),
  })),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn(),
      download: jest.fn(),
      createSignedUrl: jest.fn(),
    })),
  },
} as any

describe("Supabase Integration Tests", () => {
  let databaseService: DatabaseService

  beforeEach(() => {
    jest.clearAllMocks()
    databaseService = new DatabaseService(mockSupabaseClient)
  })

  describe("Database Connectivity", () => {
    it("should establish connection to Supabase", async () => {
      // Arrange
      const mockResponse = { data: [], error: null }
      mockSupabaseClient.from().select().limit().mockResolvedValue(mockResponse)

      // Act
      const result = await databaseService.getVideos("test-user-id")

      // Assert
      expect(mockSupabaseClient.from).toHaveBeenCalledWith("videos")
      expect(result).toEqual([])
    })

    it("should handle connection errors gracefully", async () => {
      // Arrange
      const mockError = new Error("Connection failed")
      mockSupabaseClient.from().select().eq().order().mockRejectedValue(mockError)

      // Act & Assert
      await expect(databaseService.getVideos("test-user-id")).rejects.toThrow("Connection failed")
    })

    it("should validate database schema constraints", async () => {
      // Arrange
      const invalidVideoData = {
        user_id: "", // Invalid: empty user_id
        storage_path: "test-path",
        locale: "invalid-locale", // Invalid: not a supported locale
      }

      const mockError = { message: "Invalid data", code: "VALIDATION_ERROR" }
      mockSupabaseClient.from().insert().select().single().mockResolvedValue({
        data: null,
        error: mockError,
      })

      // Act & Assert
      await expect(databaseService.createVideo(invalidVideoData as any)).rejects.toThrow("Invalid data")
    })
  })

  describe("Authentication Tests", () => {
    it("should authenticate user with valid credentials", async () => {
      // Arrange
      const credentials = { email: "<EMAIL>", password: "password123" }
      const mockSession = {
        user: { id: "user-id", email: "<EMAIL>" },
        access_token: "token",
      }
      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: { session: mockSession },
        error: null,
      })

      // Act
      const result = await mockSupabaseClient.auth.signInWithPassword(credentials)

      // Assert
      expect(result.data.session).toEqual(mockSession)
      expect(result.error).toBeNull()
    })

    it("should reject invalid credentials", async () => {
      // Arrange
      const credentials = { email: "<EMAIL>", password: "wrongpassword" }
      const mockError = { message: "Invalid credentials" }
      mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({
        data: { session: null },
        error: mockError,
      })

      // Act
      const result = await mockSupabaseClient.auth.signInWithPassword(credentials)

      // Assert
      expect(result.data.session).toBeNull()
      expect(result.error).toEqual(mockError)
    })

    it("should handle user registration", async () => {
      // Arrange
      const userData = { email: "<EMAIL>", password: "password123" }
      const mockUser = { id: "new-user-id", email: "<EMAIL>" }
      mockSupabaseClient.auth.signUp.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      })

      // Act
      const result = await mockSupabaseClient.auth.signUp(userData)

      // Assert
      expect(result.data.user).toEqual(mockUser)
      expect(result.error).toBeNull()
    })

    it("should handle user logout", async () => {
      // Arrange
      mockSupabaseClient.auth.signOut.mockResolvedValue({ error: null })

      // Act
      const result = await mockSupabaseClient.auth.signOut()

      // Assert
      expect(result.error).toBeNull()
    })
  })

  describe("CRUD Operations", () => {
    it("should create video record successfully", async () => {
      // Arrange
      const videoData = {
        user_id: "test-user-id",
        storage_path: "raw/test-user-id/video.mp4",
        locale: "en",
        expires_at: new Date().toISOString(),
        status: "uploaded" as const,
      }
      const mockCreatedVideo = { id: "video-id", ...videoData }
      mockSupabaseClient.from().insert().select().single().mockResolvedValue({
        data: mockCreatedVideo,
        error: null,
      })

      // Act
      const result = await databaseService.createVideo(videoData)

      // Assert
      expect(result).toEqual(mockCreatedVideo)
      expect(mockSupabaseClient.from).toHaveBeenCalledWith("videos")
    })

    it("should read video records for user", async () => {
      // Arrange
      const userId = "test-user-id"
      const mockVideos = [(global as any).testUtils.mockVideoData]
      mockSupabaseClient.from().select().eq().order().mockResolvedValue({
        data: mockVideos,
        error: null,
      })

      // Act
      const result = await databaseService.getVideos(userId)

      // Assert
      expect(result).toEqual(mockVideos)
      expect(mockSupabaseClient.from().select().eq).toHaveBeenCalledWith("user_id", userId)
    })

    it("should update video record", async () => {
      // Arrange
      const videoId = "test-video-id"
      const updates = { status: "transcribed" as const }
      const mockUpdatedVideo = { ...(global as any).testUtils.mockVideoData, ...updates }
      mockSupabaseClient.from().update().eq().select().single().mockResolvedValue({
        data: mockUpdatedVideo,
        error: null,
      })

      // Act
      const result = await databaseService.updateVideo(videoId, updates)

      // Assert
      expect(result).toEqual(mockUpdatedVideo)
      expect(mockSupabaseClient.from().update).toHaveBeenCalledWith(updates)
    })

    it("should handle database errors in CRUD operations", async () => {
      // Arrange
      const mockError = { message: "Database error", code: "DB_ERROR" }
      mockSupabaseClient.from().select().eq().order().mockResolvedValue({
        data: null,
        error: mockError,
      })

      // Act & Assert
      await expect(databaseService.getVideos("test-user-id")).rejects.toThrow("Database error")
    })
  })

  describe("Data Validation", () => {
    it("should enforce required fields", async () => {
      // Arrange
      const incompleteData = {
        storage_path: "test-path",
        // Missing required user_id
      }
      const mockError = { message: "user_id is required" }
      mockSupabaseClient.from().insert().select().single().mockResolvedValue({
        data: null,
        error: mockError,
      })

      // Act & Assert
      await expect(databaseService.createVideo(incompleteData as any)).rejects.toThrow("user_id is required")
    })

    it("should validate data types", async () => {
      // Arrange
      const invalidData = {
        user_id: 123, // Should be string
        storage_path: "test-path",
        locale: "en",
      }
      const mockError = { message: "Invalid data type for user_id" }
      mockSupabaseClient.from().insert().select().single().mockResolvedValue({
        data: null,
        error: mockError,
      })

      // Act & Assert
      await expect(databaseService.createVideo(invalidData as any)).rejects.toThrow("Invalid data type for user_id")
    })
  })

  describe("Storage Operations", () => {
    it("should upload file to storage", async () => {
      // Arrange
      const file = new File(["test content"], "test.mp4", { type: "video/mp4" })
      const filePath = "test-user-id/test.mp4"
      mockSupabaseClient.storage.from().upload.mockResolvedValue({
        data: { path: filePath },
        error: null,
      })

      // Act
      const result = await mockSupabaseClient.storage.from("raw").upload(filePath, file)

      // Assert
      expect(result.data.path).toBe(filePath)
      expect(result.error).toBeNull()
    })

    it("should create signed URL for file access", async () => {
      // Arrange
      const filePath = "test-user-id/test.mp4"
      const signedUrl = "https://example.com/signed-url"
      mockSupabaseClient.storage.from().createSignedUrl.mockResolvedValue({
        data: { signedUrl },
        error: null,
      })

      // Act
      const result = await mockSupabaseClient.storage.from("raw").createSignedUrl(filePath, 3600)

      // Assert
      expect(result.data.signedUrl).toBe(signedUrl)
      expect(result.error).toBeNull()
    })
  })
})
