import { notFound } from "next/navigation"
import { getRequestConfig } from "next-intl/server"
import { validateLocale, DEFAULT_LOCALE } from "@reality-scripts/lib"

// Can be imported from a shared config
const locales = ["en", "es", "fr", "de", "it", "pt", "ru", "zh", "ja", "ko", "ar", "hi"]

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  const validatedLocale = validateLocale(locale)
  
  if (!locales.includes(validatedLocale)) {
    notFound()
  }

  try {
    const messages = await import(`./locales/${validatedLocale}.json`)
    return {
      messages: messages.default
    }
  } catch (error) {
    console.warn(`No messages found for locale: ${validatedLocale}, falling back to English`)
    try {
      const fallbackMessages = await import(`./locales/${DEFAULT_LOCALE}.json`)
      return {
        messages: fallbackMessages.default
      }
    } catch (fallbackError) {
      console.error("Failed to load fallback messages:", fallbackError)
      return {
        messages: {}
      }
    }
  }
})
