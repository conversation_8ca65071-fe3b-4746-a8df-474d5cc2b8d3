import type { SupportedLocale, <PERSON><PERSON><PERSON><PERSON> } from "@reality-scripts/lib"
import { LOCALE_CONFIGS, DEFAULT_LOCALE, validateLocale, isRTL } from "@reality-scripts/lib"

export const supportedLocales = Object.values(LOCALE_CONFIGS).map((config) => ({
  value: config.code,
  label: config.nativeName,
}))

export async function getMessages(locale: string): Promise<TranslationKeys> {
  const validatedLocale = validateLocale(locale)

  try {
    const messages = await import(`../locales/${validatedLocale}.json`)
    return messages.default
  } catch (error) {
    console.warn(`No messages found for locale: ${validatedLocale}, falling back to English`)
    const fallbackMessages = await import(`../locales/${DEFAULT_LOCALE}.json`)
    return fallbackMessages.default
  }
}

export function getLocaleDirection(locale: string): "ltr" | "rtl" {
  const validatedLocale = validateLocale(locale)
  return LOCALE_CONFIGS[validatedLocale].direction
}

export function isRTLLocale(locale: SupportedLocale): boolean {
  return getLocaleDirection(locale) === "rtl"
}

// Translation helper function
export function createTranslator(messages: TranslationKeys) {
  return function t(key: string, params?: Record<string, string | number>): string {
    const keys = key.split(".")
    let value: any = messages

    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k]
      } else {
        console.warn(`Translation key not found: ${key}`)
        return key
      }
    }

    if (typeof value !== "string") {
      console.warn(`Translation value is not a string: ${key}`)
      return key
    }

    // Simple parameter substitution
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey]?.toString() || match
      })
    }

    return value
  }
}
