"use client"

import type * as React from "react"
import { useState, useRef, useCallback } from "react"
import { Upload, X, FileVideo, FileAudio, AlertCircle } from "lucide-react"
import { Button } from "./button"
import { Select } from "./select"
import { LoadingSpinner } from "./loading-spinner"
import { useToast } from "./toast"
import { createLogger, metrics, measureTime } from "@reality-scripts/lib"

const logger = createLogger("video-uploader")

export interface VideoUploaderProps {
  onUpload: (file: File, locale: string) => Promise<void>
  supportedLocales: { value: string; label: string }[]
  isUploading?: boolean
  maxFileSize?: number // in bytes
  acceptedTypes?: string[]
  className?: string
}

const DEFAULT_ACCEPTED_TYPES = [
  "video/mp4",
  "video/quicktime",
  "video/x-msvideo",
  "audio/mpeg",
  "audio/ogg",
  "audio/wav",
  "audio/mp4",
]

const DEFAULT_MAX_FILE_SIZE = 1024 * 1024 * 1024 // 1GB

export const VideoUploader: React.FC<VideoUploaderProps> = ({
  onUpload,
  supportedLocales,
  isUploading = false,
  maxFileSize = DEFAULT_MAX_FILE_SIZE,
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  className = "",
}) => {
  const [dragActive, setDragActive] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [selectedLocale, setSelectedLocale] = useState("auto")
  const [uploadProgress, setUploadProgress] = useState(0)
  const [validationError, setValidationError] = useState<string | null>(null)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const { addToast } = useToast()

  const validateFile = useCallback(
    (file: File): string | null => {
      logger.info({ fileName: file.name, fileSize: file.size, fileType: file.type }, "Validating file")

      // Check file type
      if (!acceptedTypes.includes(file.type)) {
        const error = `File type ${file.type} is not supported. Please upload a video or audio file.`
        logger.warn({ fileName: file.name, fileType: file.type }, "Invalid file type")
        metrics.increment("upload.validation.error", { reason: "invalid_type" })
        return error
      }

      // Check file size
      if (file.size > maxFileSize) {
        const error = `File size (${(file.size / 1024 / 1024).toFixed(1)}MB) exceeds the maximum allowed size (${(maxFileSize / 1024 / 1024).toFixed(1)}MB).`
        logger.warn({ fileName: file.name, fileSize: file.size, maxFileSize }, "File too large")
        metrics.increment("upload.validation.error", { reason: "file_too_large" })
        return error
      }

      // Check for empty file
      if (file.size === 0) {
        const error = "File appears to be empty. Please select a valid file."
        logger.warn({ fileName: file.name }, "Empty file")
        metrics.increment("upload.validation.error", { reason: "empty_file" })
        return error
      }

      logger.info({ fileName: file.name }, "File validation passed")
      metrics.increment("upload.validation.success")
      return null
    },
    [acceptedTypes, maxFileSize],
  )

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragActive(false)
      setValidationError(null)

      const files = e.dataTransfer.files
      if (files && files[0]) {
        const file = files[0]
        const error = validateFile(file)

        if (error) {
          setValidationError(error)
          addToast({
            type: "error",
            title: "Invalid File",
            message: error,
          })
        } else {
          setSelectedFile(file)
          logger.info({ fileName: file.name }, "File selected via drag and drop")
          metrics.increment("upload.file_selected", { method: "drag_drop" })
        }
      }
    },
    [validateFile, addToast],
  )

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setValidationError(null)

      const files = e.target.files
      if (files && files[0]) {
        const file = files[0]
        const error = validateFile(file)

        if (error) {
          setValidationError(error)
          addToast({
            type: "error",
            title: "Invalid File",
            message: error,
          })
        } else {
          setSelectedFile(file)
          logger.info({ fileName: file.name }, "File selected via file input")
          metrics.increment("upload.file_selected", { method: "file_input" })
        }
      }
    },
    [validateFile, addToast],
  )

  const handleLocaleChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedLocale(e.target.value)
    logger.info({ locale: e.target.value }, "Locale changed")
  }, [])

  const handleUpload = useCallback(async () => {
    if (!selectedFile) return

    try {
      setUploadProgress(0)
      logger.info({ fileName: selectedFile.name, locale: selectedLocale }, "Starting upload")
      metrics.increment("upload.started")

      await measureTime(
        "upload.duration",
        async () => {
          await onUpload(selectedFile, selectedLocale)
        },
        {
          fileSize: selectedFile.size.toString(),
          fileType: selectedFile.type,
          locale: selectedLocale,
        },
      )

      setSelectedFile(null)
      setUploadProgress(100)

      addToast({
        type: "success",
        title: "Upload Successful",
        message: "Your file has been uploaded and is being processed.",
      })

      logger.info({ fileName: selectedFile.name }, "Upload completed successfully")
      metrics.increment("upload.success")

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    } catch (error) {
      logger.error({ error, fileName: selectedFile.name }, "Upload failed")
      metrics.increment("upload.error")

      addToast({
        type: "error",
        title: "Upload Failed",
        message: error instanceof Error ? error.message : "An unexpected error occurred. Please try again.",
      })
    }
  }, [selectedFile, selectedLocale, onUpload, addToast])

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null)
    setValidationError(null)
    setUploadProgress(0)

    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }

    logger.info("File removed")
    metrics.increment("upload.file_removed")
  }, [])

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith("video/")) {
      return (FileVideo as any)({ className: "w-8 h-8 text-blue-500" })
    } else if (file.type.startsWith("audio/")) {
      return (FileAudio as any)({ className: "w-8 h-8 text-green-500" })
    }
    return (Upload as any)({ className: "w-8 h-8 text-gray-500" })
  }

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
          ${
            dragActive
              ? "border-indigo-500 bg-indigo-50"
              : validationError
                ? "border-red-300 bg-red-50"
                : "border-gray-300 hover:border-gray-400"
          }
          ${isUploading ? "pointer-events-none opacity-50" : ""}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
            <LoadingSpinner size="lg" text="Uploading..." />
          </div>
        )}

        <div className="space-y-6">
          {!selectedFile ? (
            <>
              <div className="flex justify-center">
                {(Upload as any)({ className: "w-12 h-12 text-gray-400" })}
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Upload your video or audio file</h3>
                <p className="text-sm text-gray-500 mb-4">Drag and drop your file here, or click to browse</p>
                <Button onClick={() => fileInputRef.current?.click()} variant="outline" disabled={isUploading}>
                  Select File
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  className="hidden"
                  accept={acceptedTypes.join(",")}
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
              </div>
            </>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-3">
                {getFileIcon(selectedFile)}
                <div className="text-left">
                  <p className="font-medium text-gray-900 truncate max-w-xs">{selectedFile.name}</p>
                  <p className="text-sm text-gray-500">{formatFileSize(selectedFile.size)}</p>
                </div>
                <button
                  onClick={handleRemoveFile}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  disabled={isUploading}
                >
                  {(X as any)({ className: "w-5 h-5" })}
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label htmlFor="locale" className="block text-sm font-medium text-gray-700 mb-2">
                    Language
                  </label>
                  <Select
                    id="locale"
                    value={selectedLocale}
                    onChange={handleLocaleChange}
                    options={[{ value: "auto", label: "Auto-detect" }, ...supportedLocales]}
                    disabled={isUploading}
                  />
                </div>

                <Button onClick={handleUpload} isLoading={isUploading} disabled={isUploading} className="w-full">
                  {isUploading ? "Uploading..." : "Upload File"}
                </Button>
              </div>
            </div>
          )}

          {validationError && (
            <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
              {(AlertCircle as any)({ className: "w-5 h-5 flex-shrink-0" })}
              <p className="text-sm">{validationError}</p>
            </div>
          )}
        </div>
      </div>

      <div className="mt-6 text-sm text-gray-500 space-y-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-700 mb-1">Supported formats:</h4>
            <p>MP4, MOV, AVI, MP3, OGG, WAV</p>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-1">Maximum file size:</h4>
            <p>{formatFileSize(maxFileSize)}</p>
          </div>
        </div>
        <div className="pt-2 border-t border-gray-200">
          <p className="text-xs">
            <strong>Note:</strong> Videos will be automatically deleted after 72 hours unless extended. Transcripts and
            low-resolution proxies will be preserved indefinitely.
          </p>
        </div>
      </div>
    </div>
  )
}
