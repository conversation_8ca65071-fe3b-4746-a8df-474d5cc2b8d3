import type { SupportedLocale } from "./types"
import { DEFAULT_LOCALE, LOCALE_CONFIGS } from "./config"

export const validateLocale = (locale: string): SupportedLocale => {
  if (locale in LOCALE_CONFIGS) {
    return locale as SupportedLocale
  }
  return DEFAULT_LOCALE
}

export const formatLocaleForUrl = (locale: SupportedLocale): string => {
  return locale === DEFAULT_LOCALE ? "" : `/${locale}`
}

export const parseLocaleFromUrl = (pathname: string): SupportedLocale => {
  const segments = pathname.split("/").filter(Boolean)
  if (segments.length > 0 && segments[0] && segments[0] in LOCALE_CONFIGS) {
    return segments[0] as SupportedLocale
  }
  return DEFAULT_LOCALE
}

export const getLocalizedPath = (path: string, locale: SupportedLocale): string => {
  const localePrefix = formatLocaleForUrl(locale)
  return `${localePrefix}${path}`
}

export const removeLocaleFromPath = (path: string): string => {
  const segments = path.split("/").filter(Boolean)
  if (segments.length > 0 && segments[0] && segments[0] in LOCALE_CONFIGS) {
    return "/" + segments.slice(1).join("/")
  }
  return path
}
