/**
 * Video Upload End-to-End Tests
 *
 * Tests the complete video upload workflow including file selection,
 * validation, upload progress, and error handling.
 */

import { test, expect } from "@playwright/test"
import path from "path"

test.describe("Video Upload Workflow", () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.goto("/dashboard")
  })

  test("should upload video file successfully", async ({ page }) => {
    // Navigate to upload page
    await page.click("text=Upload New")
    await expect(page).toHaveURL(/.*upload/)

    // Upload a test video file
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(path.join(__dirname, "fixtures", "test-video.mp4"))

    // Verify file is selected
    await expect(page.locator("text=test-video.mp4")).toBeVisible()

    // Select language
    await page.selectOption("select", "en")

    // Click upload
    await page.click('button:has-text("Upload File")')

    // Verify upload progress
    await expect(page.locator("text=Uploading...")).toBeVisible()

    // Verify success message
    await expect(page.locator("text=Upload Successful")).toBeVisible()

    // Should redirect to dashboard
    await expect(page).toHaveURL(/.*dashboard/)
  })

  test("should validate file types", async ({ page }) => {
    await page.goto("/dashboard/upload")

    // Try to upload invalid file type
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(path.join(__dirname, "fixtures", "test-document.pdf"))

    // Should show error message
    await expect(page.locator("text=File type")).toBeVisible()
    await expect(page.locator("text=not supported")).toBeVisible()
  })

  test("should handle drag and drop upload", async ({ page }) => {
    await page.goto("/dashboard/upload")

    // Simulate drag and drop
    const dropZone = page.locator('[data-testid="drop-zone"]')

    // Create a file for drag and drop
    const buffer = Buffer.from("fake video content")
    await page.evaluate(async (buffer) => {
      const file = new File([new Uint8Array(buffer)], "test-video.mp4", {
        type: "video/mp4",
      })

      const dropZone = document.querySelector('[data-testid="drop-zone"]')
      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)
      const event = new DragEvent("drop", {
        dataTransfer,
      })
      dropZone?.dispatchEvent(event)
    }, Array.from(buffer))

    // Verify file is selected
    await expect(page.locator("text=test-video.mp4")).toBeVisible()
  })

  test("should handle upload errors gracefully", async ({ page }) => {
    // Mock network failure
    await page.route("**/api/upload", (route) => {
      route.abort("failed")
    })

    await page.goto("/dashboard/upload")

    // Upload file
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(path.join(__dirname, "fixtures", "test-video.mp4"))
    await page.click('button:has-text("Upload File")')

    // Should show error message
    await expect(page.locator("text=Upload Failed")).toBeVisible()
  })
})
