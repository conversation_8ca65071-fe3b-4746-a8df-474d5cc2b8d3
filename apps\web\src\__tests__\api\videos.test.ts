/**
 * API Endpoint Tests for Video Management
 *
 * Comprehensive tests for all video-related API endpoints including
 * CRUD operations, file uploads, and error handling scenarios.
 */

import { createMocks } from "node-mocks-http"
import { NextRequest } from "next/server"
import { GET as getVideos, POST as createVideo } from "@/app/api/videos/route"
import { GET as getVideo, PUT as updateVideo } from "@/app/api/videos/[id]/route"
import { POST as extendTTL } from "@/app/api/videos/[id]/extend-ttl/route"
import { GET as exportVideo } from "@/app/api/videos/[id]/export/[format]/route"

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getSession: jest.fn(),
    getUser: jest.fn(),
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn(),
  })),
  storage: {
    from: jest.fn(() => ({
      upload: jest.fn(),
      download: jest.fn(),
      createSignedUrl: jest.fn(),
      remove: jest.fn(),
    })),
  },
}

// Mock the Supabase client creation
jest.mock("@supabase/auth-helpers-nextjs", () => ({
  createRouteHandlerClient: jest.fn(() => mockSupabaseClient),
}))

describe("Video API Endpoints", () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Default authenticated session
    mockSupabaseClient.auth.getSession.mockResolvedValue({
      data: {
        session: {
          user: { id: "test-user-id", email: "<EMAIL>" },
          access_token: "mock-token",
        },
      },
      error: null,
    })
  })

  describe("GET /api/videos", () => {
    it("should return user videos successfully", async () => {
      // Arrange
      const mockVideos = [
        {
          id: "video-1",
          user_id: "test-user-id",
          storage_path: "raw/test-user-id/video1.mp4",
          status: "transcribed",
          created_at: "2023-01-01T00:00:00Z",
        },
        {
          id: "video-2",
          user_id: "test-user-id",
          storage_path: "raw/test-user-id/video2.mp4",
          status: "processing",
          created_at: "2023-01-02T00:00:00Z",
        },
      ]

      mockSupabaseClient.from().select().eq().order().mockResolvedValue({
        data: mockVideos,
        error: null,
      })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos")

      // Act
      const response = await getVideos(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data).toEqual(mockVideos)
      expect(mockSupabaseClient.from).toHaveBeenCalledWith("videos")
    })

    it("should handle unauthorized access", async () => {
      // Arrange
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null,
      })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos")

      // Act
      const response = await getVideos(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(401)
      expect(data.error).toBe("Unauthorized")
    })

    it("should handle database errors", async () => {
      // Arrange
      mockSupabaseClient
        .from()
        .select()
        .eq()
        .order()
        .mockResolvedValue({
          data: null,
          error: { message: "Database connection failed" },
        })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos")

      // Act
      const response = await getVideos(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(500)
      expect(data.error).toContain("Database connection failed")
    })
  })

  describe("POST /api/videos", () => {
    it("should create video record successfully", async () => {
      // Arrange
      const videoData = {
        storage_path: "raw/test-user-id/new-video.mp4",
        locale: "en",
        status: "uploaded",
      }

      const mockCreatedVideo = {
        id: "new-video-id",
        user_id: "test-user-id",
        ...videoData,
        created_at: "2023-01-01T00:00:00Z",
      }

      mockSupabaseClient.from().insert().select().single().mockResolvedValue({
        data: mockCreatedVideo,
        error: null,
      })

      const { req } = createMocks({
        method: "POST",
        body: videoData,
      })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos", {
        method: "POST",
        body: JSON.stringify(videoData),
        headers: { "Content-Type": "application/json" },
      })

      // Act
      const response = await createVideo(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(201)
      expect(data).toEqual(mockCreatedVideo)
    })

    it("should validate required fields", async () => {
      // Arrange
      const invalidData = {
        // Missing required storage_path
        locale: "en",
      }

      const { req } = createMocks({
        method: "POST",
        body: invalidData,
      })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos", {
        method: "POST",
        body: JSON.stringify(invalidData),
        headers: { "Content-Type": "application/json" },
      })

      // Act
      const response = await createVideo(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toContain("storage_path is required")
    })

    it("should handle malformed JSON", async () => {
      // Arrange
      const { req } = createMocks({ method: "POST" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos", {
        method: "POST",
        body: "invalid json",
        headers: { "Content-Type": "application/json" },
      })

      // Act
      const response = await createVideo(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toContain("Invalid JSON")
    })
  })

  describe("GET /api/videos/[id]", () => {
    it("should return specific video", async () => {
      // Arrange
      const mockVideo = {
        id: "video-1",
        user_id: "test-user-id",
        storage_path: "raw/test-user-id/video1.mp4",
        status: "transcribed",
      }

      mockSupabaseClient.from().select().eq().single().mockResolvedValue({
        data: mockVideo,
        error: null,
      })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1")

      // Act
      const response = await getVideo(request, { params: Promise.resolve({ id: "video-1" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data).toEqual(mockVideo)
    })

    it("should return 404 for non-existent video", async () => {
      // Arrange
      mockSupabaseClient
        .from()
        .select()
        .eq()
        .single()
        .mockResolvedValue({
          data: null,
          error: { code: "PGRST116", message: "No rows found" },
        })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/non-existent")

      // Act
      const response = await getVideo(request, { params: Promise.resolve({ id: "non-existent" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(404)
      expect(data.error).toBe("Video not found")
    })

    it("should prevent access to other users videos", async () => {
      // Arrange
      const otherUserVideo = {
        id: "video-1",
        user_id: "other-user-id",
        storage_path: "raw/other-user-id/video1.mp4",
      }

      mockSupabaseClient.from().select().eq().single().mockResolvedValue({
        data: otherUserVideo,
        error: null,
      })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1")

      // Act
      const response = await getVideo(request, { params: Promise.resolve({ id: "video-1" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(403)
      expect(data.error).toBe("Access denied")
    })
  })

  describe("PUT /api/videos/[id]", () => {
    it("should update video successfully", async () => {
      // Arrange
      const updates = { status: "transcribed" }
      const mockUpdatedVideo = {
        id: "video-1",
        user_id: "test-user-id",
        status: "transcribed",
      }

      mockSupabaseClient.from().update().eq().select().single().mockResolvedValue({
        data: mockUpdatedVideo,
        error: null,
      })

      const { req } = createMocks({
        method: "PUT",
        body: updates,
      })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1", {
        method: "PUT",
        body: JSON.stringify(updates),
        headers: { "Content-Type": "application/json" },
      })

      // Act
      const response = await updateVideo(request, { params: Promise.resolve({ id: "video-1" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data).toEqual(mockUpdatedVideo)
    })

    it("should validate update data", async () => {
      // Arrange
      const invalidUpdates = { user_id: "different-user" } // Should not be allowed

      const { req } = createMocks({
        method: "PUT",
        body: invalidUpdates,
      })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1", {
        method: "PUT",
        body: JSON.stringify(invalidUpdates),
        headers: { "Content-Type": "application/json" },
      })

      // Act
      const response = await updateVideo(request, { params: Promise.resolve({ id: "video-1" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toContain("Invalid update field")
    })
  })

  describe("POST /api/videos/[id]/extend-ttl", () => {
    it("should extend video TTL successfully", async () => {
      // Arrange
      const mockVideo = {
        id: "video-1",
        user_id: "test-user-id",
        expires_at: "2023-01-01T00:00:00Z",
      }

      mockSupabaseClient.from().select().eq().single().mockResolvedValue({
        data: mockVideo,
        error: null,
      })

      mockSupabaseClient
        .from()
        .update()
        .eq()
        .mockResolvedValue({
          data: { ...mockVideo, expires_at: "2023-01-04T00:00:00Z" },
          error: null,
        })

      const { req } = createMocks({ method: "POST" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1/extend-ttl")

      // Act
      const response = await extendTTL(request, { params: Promise.resolve({ id: "video-1" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })

    it("should handle videos without expiry", async () => {
      // Arrange
      const mockVideo = {
        id: "video-1",
        user_id: "test-user-id",
        expires_at: null, // Pro user video
      }

      mockSupabaseClient.from().select().eq().single().mockResolvedValue({
        data: mockVideo,
        error: null,
      })

      const { req } = createMocks({ method: "POST" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1/extend-ttl")

      // Act
      const response = await extendTTL(request, { params: Promise.resolve({ id: "video-1" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toContain("Video does not have expiry")
    })
  })

  describe("GET /api/videos/[id]/export/[format]", () => {
    it("should export video transcript in SRT format", async () => {
      // Arrange
      const mockVideo = {
        id: "video-1",
        user_id: "test-user-id",
      }

      const mockTranscription = {
        id: "transcription-1",
        video_id: "video-1",
        words: [
          { text: "Hello", start: 0, end: 500 },
          { text: "world", start: 500, end: 1000 },
        ],
      }

      mockSupabaseClient
        .from()
        .select()
        .eq()
        .single()
        .mockResolvedValueOnce({ data: mockVideo, error: null })
        .mockResolvedValueOnce({ data: mockTranscription, error: null })

      mockSupabaseClient.storage.from().createSignedUrl.mockResolvedValue({
        data: { signedUrl: "https://example.com/export.srt" },
        error: null,
      })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1/export/srt")

      // Act
      const response = await exportVideo(request, {
        params: Promise.resolve({ id: "video-1", format: "srt" }),
      })

      // Assert
      expect(response.status).toBe(302) // Redirect to signed URL
    })

    it("should handle unsupported export formats", async () => {
      // Arrange
      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1/export/invalid")

      // Act
      const response = await exportVideo(request, {
        params: Promise.resolve({ id: "video-1", format: "invalid" }),
      })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toContain("Unsupported format")
    })
  })

  describe("Error Handling and Edge Cases", () => {
    it("should handle network timeouts", async () => {
      // Arrange
      mockSupabaseClient
        .from()
        .select()
        .eq()
        .order()
        .mockImplementation(() => {
          return new Promise((_, reject) => {
            setTimeout(() => reject(new Error("Network timeout")), 100)
          })
        })

      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos")

      // Act
      const response = await getVideos(request)
      const data = await response.json()

      // Assert
      expect(response.status).toBe(500)
      expect(data.error).toContain("Network timeout")
    })

    it("should handle malformed video IDs", async () => {
      // Arrange
      const { req } = createMocks({ method: "GET" })
      const request = new NextRequest(req.url || "http://localhost:3000/api/videos/invalid-uuid")

      // Act
      const response = await getVideo(request, { params: Promise.resolve({ id: "invalid-uuid" }) })
      const data = await response.json()

      // Assert
      expect(response.status).toBe(400)
      expect(data.error).toContain("Invalid video ID format")
    })

    it("should handle concurrent requests gracefully", async () => {
      // Arrange
      const mockVideo = {
        id: "video-1",
        user_id: "test-user-id",
        status: "processing",
      }

      mockSupabaseClient.from().select().eq().single().mockResolvedValue({
        data: mockVideo,
        error: null,
      })

      const { req } = createMocks({ method: "GET" })
      const request1 = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1")
      const request2 = new NextRequest(req.url || "http://localhost:3000/api/videos/video-1")

      // Act
      const [response1, response2] = await Promise.all([
        getVideo(request1, { params: Promise.resolve({ id: "video-1" }) }),
        getVideo(request2, { params: Promise.resolve({ id: "video-1" }) }),
      ])

      // Assert
      expect(response1.status).toBe(200)
      expect(response2.status).toBe(200)
    })
  })
})
