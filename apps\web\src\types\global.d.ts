/**
 * Global Type Definitions
 * 
 * This file contains global type definitions for the application,
 * including environment variables, window extensions, and global utilities.
 */

declare global {
  interface Window {
    // Performance API extensions
    performance: Performance & {
      mark(name: string): void
      measure(name: string, startMark?: string, endMark?: string): PerformanceMeasure | undefined
      getEntriesByName(name: string, type?: string): PerformanceEntry[]
      getEntriesByType(type: string): PerformanceEntry[]
    }
  }

  // Environment variables
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test'
      NEXT_PUBLIC_SUPABASE_URL: string
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string
      SUPABASE_SERVICE_ROLE_KEY: string
      SUPABASE_JWT_SECRET?: string
      HF_API_TOKEN?: string
      WHISPER_MODEL: 'tiny' | 'base' | 'small' | 'medium' | 'large' | 'large-v2' | 'large-v3'
      ASR_BACKEND: 'hf' | 'local'
      LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error'
      ENABLE_ANALYTICS?: string
      ENABLE_MONITORING?: string
    }
  }

  // DataTransfer API extensions for testing
  interface DataTransfer {
    files: FileList
  }
}

export {}
