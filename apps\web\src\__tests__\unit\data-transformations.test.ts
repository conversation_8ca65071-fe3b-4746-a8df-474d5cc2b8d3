/**
 * Data Transformation Tests
 *
 * Tests for all data transformation logic including format conversions,
 * validation, sanitization, and type transformations.
 */

import {
  formatTime,
  parseVideoMetadata,
  transformTranscriptionData,
  validateUploadData,
  sanitizeUserInput,
  convertTimestamps,
  generateExportData,
} from "@/lib/data-transformations"

describe("Data Transformation Functions", () => {
  describe("formatTime", () => {
    it("should format seconds to MM:SS format", () => {
      expect(formatTime(0)).toBe("0:00")
      expect(formatTime(30)).toBe("0:30")
      expect(formatTime(60)).toBe("1:00")
      expect(formatTime(90)).toBe("1:30")
      expect(formatTime(3661)).toBe("61:01")
    })

    it("should handle edge cases", () => {
      expect(formatTime(-1)).toBe("0:00")
      expect(formatTime(Number.NaN)).toBe("0:00")
      expect(formatTime(Number.POSITIVE_INFINITY)).toBe("0:00")
    })

    it("should format milliseconds when specified", () => {
      expect(formatTime(1500, true)).toBe("0:01.500")
      expect(formatTime(65500, true)).toBe("1:05.500")
    })
  })

  describe("parseVideoMetadata", () => {
    it("should extract metadata from video file", () => {
      const mockFile = new File([""], "test-video.mp4", {
        type: "video/mp4",
        lastModified: 1640995200000, // 2022-01-01
      })

      const metadata = parseVideoMetadata(mockFile)

      expect(metadata).toEqual({
        name: "test-video.mp4",
        type: "video/mp4",
        size: 0,
        lastModified: new Date(1640995200000),
        extension: "mp4",
        isVideo: true,
        isAudio: false,
      })
    })

    it("should handle audio files", () => {
      const mockFile = new File([""], "test-audio.mp3", {
        type: "audio/mpeg",
      })

      const metadata = parseVideoMetadata(mockFile)

      expect(metadata.isVideo).toBe(false)
      expect(metadata.isAudio).toBe(true)
      expect(metadata.extension).toBe("mp3")
    })

    it("should handle files without extensions", () => {
      const mockFile = new File([""], "test-file", {
        type: "video/mp4",
      })

      const metadata = parseVideoMetadata(mockFile)

      expect(metadata.extension).toBe("")
      expect(metadata.name).toBe("test-file")
    })
  })

  describe("transformTranscriptionData", () => {
    it("should transform raw transcription to standard format", () => {
      const rawTranscription = {
        text: "Hello world. This is a test.",
        segments: [
          {
            start: 0.0,
            end: 2.5,
            text: "Hello world.",
            words: [
              { word: "Hello", start: 0.0, end: 0.5, confidence: 0.95 },
              { word: "world", start: 0.6, end: 1.2, confidence: 0.98 },
            ],
          },
          {
            start: 2.5,
            end: 5.0,
            text: "This is a test.",
            words: [
              { word: "This", start: 2.5, end: 2.8, confidence: 0.92 },
              { word: "is", start: 2.9, end: 3.1, confidence: 0.99 },
              { word: "a", start: 3.2, end: 3.3, confidence: 0.97 },
              { word: "test", start: 3.4, end: 3.8, confidence: 0.94 },
            ],
          },
        ],
        language: "en",
      }

      const transformed = transformTranscriptionData(rawTranscription)

      expect(transformed).toEqual({
        text: "Hello world. This is a test.",
        words: [
          { text: "Hello", start: 0, end: 500, confidence: 0.95 },
          { text: "world", start: 600, end: 1200, confidence: 0.98 },
          { text: "This", start: 2500, end: 2800, confidence: 0.92 },
          { text: "is", start: 2900, end: 3100, confidence: 0.99 },
          { text: "a", start: 3200, end: 3300, confidence: 0.97 },
          { text: "test", start: 3400, end: 3800, confidence: 0.94 },
        ],
        segments: [
          { start: 0, end: 2500, text: "Hello world." },
          { start: 2500, end: 5000, text: "This is a test." },
        ],
        language: "en",
        duration: 5000,
      })
    })

    it("should handle empty transcription", () => {
      const rawTranscription = {
        text: "",
        segments: [],
        language: "en",
      }

      const transformed = transformTranscriptionData(rawTranscription)

      expect(transformed).toEqual({
        text: "",
        words: [],
        segments: [],
        language: "en",
        duration: 0,
      })
    })

    it("should handle missing confidence scores", () => {
      const rawTranscription = {
        text: "Hello",
        segments: [
          {
            start: 0.0,
            end: 1.0,
            text: "Hello",
            words: [
              { word: "Hello", start: 0.0, end: 1.0 }, // No confidence
            ],
          },
        ],
        language: "en",
      }

      const transformed = transformTranscriptionData(rawTranscription)

      expect(transformed.words[0].confidence).toBe(1.0) // Default confidence
    })
  })

  describe("validateUploadData", () => {
    it("should validate correct upload data", () => {
      const uploadData = {
        file: new File(["content"], "test.mp4", { type: "video/mp4" }),
        locale: "en",
        title: "Test Video",
      }

      const result = validateUploadData(uploadData)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
    })

    it("should reject invalid file types", () => {
      const uploadData = {
        file: new File(["content"], "test.txt", { type: "text/plain" }),
        locale: "en",
      }

      const result = validateUploadData(uploadData)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain("Invalid file type")
    })

    it("should reject files that are too large", () => {
      const largeContent = new Array(1024 * 1024 * 1024 + 1).fill("a").join("") // > 1GB
      const uploadData = {
        file: new File([largeContent], "test.mp4", { type: "video/mp4" }),
        locale: "en",
      }

      const result = validateUploadData(uploadData)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain("File size exceeds limit")
    })

    it("should reject invalid locales", () => {
      const uploadData = {
        file: new File(["content"], "test.mp4", { type: "video/mp4" }),
        locale: "invalid-locale",
      }

      const result = validateUploadData(uploadData)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain("Invalid locale")
    })

    it("should sanitize and validate title", () => {
      const uploadData = {
        file: new File(["content"], "test.mp4", { type: "video/mp4" }),
        locale: "en",
        title: '<script>alert("xss")</script>My Video',
      }

      const result = validateUploadData(uploadData)

      expect(result.isValid).toBe(true)
      expect(result.sanitizedData.title).toBe("My Video") // XSS removed
    })
  })

  describe("sanitizeUserInput", () => {
    it("should remove HTML tags", () => {
      const input = '<script>alert("xss")</script>Hello <b>World</b>'
      const sanitized = sanitizeUserInput(input)
      expect(sanitized).toBe("Hello World")
    })

    it("should trim whitespace", () => {
      const input = "  Hello World  "
      const sanitized = sanitizeUserInput(input)
      expect(sanitized).toBe("Hello World")
    })

    it("should handle empty strings", () => {
      expect(sanitizeUserInput("")).toBe("")
      expect(sanitizeUserInput("   ")).toBe("")
    })

    it("should preserve safe characters", () => {
      const input = "Hello World! 123 @#$%"
      const sanitized = sanitizeUserInput(input)
      expect(sanitized).toBe("Hello World! 123 @#$%")
    })
  })

  describe("convertTimestamps", () => {
    it("should convert seconds to milliseconds", () => {
      const timestamps = [0, 1.5, 3.75, 10]
      const converted = convertTimestamps(timestamps, "seconds", "milliseconds")
      expect(converted).toEqual([0, 1500, 3750, 10000])
    })

    it("should convert milliseconds to seconds", () => {
      const timestamps = [0, 1500, 3750, 10000]
      const converted = convertTimestamps(timestamps, "milliseconds", "seconds")
      expect(converted).toEqual([0, 1.5, 3.75, 10])
    })

    it("should handle empty arrays", () => {
      const converted = convertTimestamps([], "seconds", "milliseconds")
      expect(converted).toEqual([])
    })

    it("should handle invalid timestamps", () => {
      const timestamps = [0, Number.NaN, -1, Number.POSITIVE_INFINITY]
      const converted = convertTimestamps(timestamps, "seconds", "milliseconds")
      expect(converted).toEqual([0, 0, 0, 0]) // Invalid values become 0
    })
  })

  describe("generateExportData", () => {
    const mockTranscription = {
      words: [
        { text: "Hello", start: 0, end: 500 },
        { text: "world", start: 500, end: 1000 },
        { text: "This", start: 2000, end: 2500 },
        { text: "is", start: 2500, end: 2750 },
        { text: "a", start: 2750, end: 2900 },
        { text: "test", start: 2900, end: 3400 },
      ],
      text: "Hello world This is a test",
      language: "en",
    }

    it("should generate SRT format", () => {
      const srt = generateExportData(mockTranscription, "srt")

      expect(srt).toContain("1\n00:00:00,000 --> 00:00:01,000\nHello world")
      expect(srt).toContain("2\n00:00:02,000 --> 00:00:03,400\nThis is a test")
    })

    it("should generate VTT format", () => {
      const vtt = generateExportData(mockTranscription, "vtt")

      expect(vtt.startsWith("WEBVTT")).toBe(true)
      expect(vtt).toContain("00:00:00.000 --> 00:00:01.000\nHello world")
      expect(vtt).toContain("00:00:02.000 --> 00:00:03.400\nThis is a test")
    })

    it("should generate plain text format", () => {
      const txt = generateExportData(mockTranscription, "txt")

      expect(txt).toBe("Hello world This is a test")
    })

    it("should generate JSON format", () => {
      const json = generateExportData(mockTranscription, "json")
      const parsed = JSON.parse(json)

      expect(parsed).toEqual(mockTranscription)
    })

    it("should handle unsupported formats", () => {
      expect(() => {
        generateExportData(mockTranscription, "unsupported")
      }).toThrow("Unsupported export format")
    })

    it("should handle empty transcription", () => {
      const emptyTranscription = {
        words: [],
        text: "",
        language: "en",
      }

      const srt = generateExportData(emptyTranscription, "srt")
      expect(srt).toBe("")

      const txt = generateExportData(emptyTranscription, "txt")
      expect(txt).toBe("")
    })
  })

  describe("Complex Data Transformations", () => {
    it("should handle nested data structures", () => {
      const complexData = {
        video: {
          metadata: {
            duration: 120.5,
            resolution: "1920x1080",
            fps: 30,
          },
          transcription: {
            segments: [
              {
                speaker: "Speaker 1",
                start: 0,
                end: 5000,
                words: [{ text: "Hello", start: 0, end: 500, confidence: 0.95 }],
              },
            ],
          },
        },
      }

      const flattened = flattenVideoData(complexData)

      expect(flattened).toEqual({
        duration: 120.5,
        resolution: "1920x1080",
        fps: 30,
        segments: complexData.video.transcription.segments,
        totalWords: 1,
        averageConfidence: 0.95,
      })
    })

    it("should batch process multiple files", () => {
      const files = [
        new File([""], "video1.mp4", { type: "video/mp4" }),
        new File([""], "video2.mp4", { type: "video/mp4" }),
        new File([""], "audio1.mp3", { type: "audio/mpeg" }),
      ]

      const processed = batchProcessFiles(files)

      expect(processed).toHaveLength(3)
      expect(processed[0]?.type).toBe("video")
      expect(processed[1]?.type).toBe("video")
      expect(processed[2]?.type).toBe("audio")
    })

    it("should merge transcription segments", () => {
      const segments = [
        { start: 0, end: 1000, text: "Hello" },
        { start: 1000, end: 2000, text: "world" },
        { start: 2000, end: 3000, text: "This is" },
        { start: 3000, end: 4000, text: "a test" },
      ]

      const merged = mergeSegments(segments, 2000) // Merge segments within 2 seconds

      expect(merged).toEqual([
        { start: 0, end: 2000, text: "Hello world" },
        { start: 2000, end: 4000, text: "This is a test" },
      ])
    })
  })
})

// Helper functions for complex transformations
function flattenVideoData(data: any) {
  return {
    duration: data.video.metadata.duration,
    resolution: data.video.metadata.resolution,
    fps: data.video.metadata.fps,
    segments: data.video.transcription.segments,
    totalWords: data.video.transcription.segments.reduce(
      (total: number, segment: any) => total + segment.words.length,
      0,
    ),
    averageConfidence:
      data.video.transcription.segments
        .flatMap((segment: any) => segment.words)
        .reduce((sum: number, word: any) => sum + word.confidence, 0) /
      data.video.transcription.segments.flatMap((segment: any) => segment.words).length,
  }
}

function batchProcessFiles(files: File[]) {
  return files.map((file) => ({
    name: file.name,
    type: file.type.startsWith("video/") ? "video" : "audio",
    size: file.size,
    processed: true,
  }))
}

function mergeSegments(segments: any[], maxGap: number) {
  const merged = []
  let current = segments[0]

  for (let i = 1; i < segments.length; i++) {
    const next = segments[i]

    if (next.start - current.end <= maxGap) {
      current = {
        start: current.start,
        end: next.end,
        text: `${current.text} ${next.text}`,
      }
    } else {
      merged.push(current)
      current = next
    }
  }

  merged.push(current)
  return merged
}
