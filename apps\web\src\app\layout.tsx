import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import { NextIntlClientProvider } from "next-intl"
import { getMessages, getLocaleDirection } from "@/lib/i18n"
import { ErrorBoundary, ToastProvider } from "@reality-scripts/ui"
import { validateLocale } from "@reality-scripts/lib"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "RealityScripts - Video to Text Intelligence",
  description: "Transform your videos into rich, searchable, and interactive transcripts",
}

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode
  params: { locale?: string }
}) {
  const locale = validateLocale(params?.locale || "en")
  const messages = await getMessages(locale)
  const direction = getLocaleDirection(locale)

  return (
    <html lang={locale} dir={direction}>
      <body className={inter.className}>
        <ErrorBoundary>
          <ToastProvider>
            <NextIntlClientProvider locale={locale} messages={messages as any}>
              <div className={`min-h-screen ${direction === "rtl" ? "font-arabic" : ""}`}>{children}</div>
            </NextIntlClientProvider>
          </ToastProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
